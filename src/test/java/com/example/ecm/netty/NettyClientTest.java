package com.example.ecm.netty;

import com.example.ecm.service.RedisCacheService;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

@Slf4j
public class NettyClientTest {
    public static void main(String[] args) throws Exception {
        // 先刷新缓存
        ConfigurableApplicationContext context = SpringApplication.run(NettyClientTest.class, args);
        RedisCacheService cacheService = context.getBean(RedisCacheService.class);
        cacheService.refreshCache();
        
        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap bootstrap = new Bootstrap()
                .group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        ch.pipeline()
                            .addLast(new StringDecoder())
                            .addLast(new StringEncoder())
                            .addLast(new SimpleChannelInboundHandler<String>() {
                                @Override
                                protected void channelRead0(ChannelHandlerContext ctx, String msg) {
                                    log.info("收到服务器响应: {}", msg);
                                }
                            });
                    }
                });

            // 连接服务器
            Channel channel = bootstrap.connect("localhost", 9999).sync().channel();

            // 发送测试报文
            // 1. 注册报文
            String registerMsg = "QN=20240214151600000;ST=32;CN=1013;PW=123456;MN=ABCDEF123456;Flag=4;CP=&&&&";
            channel.writeAndFlush(registerMsg);
            Thread.sleep(1000);

            // 2. 时间同步报文
            String timeMsg = "QN=20240214151700000;ST=32;CN=1012;PW=123456;MN=ABCDEF123456;Flag=4;CP=&&&&";
            channel.writeAndFlush(timeMsg);
            Thread.sleep(1000);

            // 3. 数据上报报文
            String dataMsg = "QN=20240214151800000;ST=32;CN=2011;PW=123456;MN=ABCDEF123456;Flag=4;CP=&&DataTime=20240214151800;DEVICE1-Ia=10.5,DEVICE1-Ib=11.2,DEVICE1-Ic=10.8,DEVICE1-P=2000&&";
            channel.writeAndFlush(dataMsg);
            Thread.sleep(1000);

            // 等待响应
            Thread.sleep(5000);
        } finally {
            group.shutdownGracefully();
            context.close();
        }
    }
} 
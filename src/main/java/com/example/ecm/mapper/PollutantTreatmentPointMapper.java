package com.example.ecm.mapper;

import com.example.ecm.entity.PollutantTreatmentPoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface PollutantTreatmentPointMapper {
    @Select("SELECT * FROM ecm_pollutant_treatment_point")
    List<PollutantTreatmentPoint> findAll();
    
    @Update("UPDATE ecm_pollutant_treatment_point SET LASTTIME = #{lastTime} WHERE ID = #{id}")
    void updateLastTime(@Param("id") Integer id, @Param("lastTime") String lastTime);

    @Update("UPDATE ecm_pollutant_treatment_point SET " +
            "LASTTIME = #{lastTime}, " +
            "IA = #{ia}, " +
            "IB = #{ib}, " +
            "IC = #{ic}, " +
            "UA = #{ua}, " +
            "UB = #{ub}, " +
            "UC = #{uc}, " +
            "ACTIVE_POWER = #{activePower}, " +
            "ACTIVE_ENERGY = #{activeEnergy} " +
            "WHERE ID = #{id}")
    void updateLastTimeAndPowerData(@Param("id") Integer id,
                                   @Param("lastTime") String lastTime,
                                   @Param("ia") BigDecimal ia,
                                   @Param("ib") BigDecimal ib,
                                   @Param("ic") BigDecimal ic,
                                   @Param("ua") BigDecimal ua,
                                   @Param("ub") BigDecimal ub,
                                   @Param("uc") BigDecimal uc,
                                   @Param("activePower") BigDecimal activePower,
                                   @Param("activeEnergy") BigDecimal activeEnergy);
}
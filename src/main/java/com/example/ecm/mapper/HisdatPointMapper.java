package com.example.ecm.mapper;

import com.example.ecm.entity.HisdatPoint;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface HisdatPointMapper {

    void insertOutputPoint(@Param("enterpriseId") String enterpriseId, @Param("point") HisdatPoint point);

    void insertTreatmentPoint(@Param("enterpriseId") String enterpriseId, @Param("point") HisdatPoint point);
} 
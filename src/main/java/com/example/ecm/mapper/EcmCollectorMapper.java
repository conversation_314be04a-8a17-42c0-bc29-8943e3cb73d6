package com.example.ecm.mapper;

import com.example.ecm.entity.EcmCollector;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface EcmCollectorMapper {
    @Select("SELECT ID as id, ENTERPRISE_ID as enterpriseId, LOCATION as location, " +
            "MN as mn, PASSWORD as password, SN as sn, MEMO as memo, " +
            "CREATETIME as createTime, UPDATETIME as updateTime FROM ecm_collector")
    List<EcmCollector> findAll();
} 
package com.example.ecm.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class HisdatPoint implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer pointId;
    private Date sampDateTime;
    private Date rcvDateTime;
    private Date insertDateTime;
    private BigDecimal ia; // A相电流
    private BigDecimal ib; // B相电流
    private BigDecimal ic; // C相电流
    private BigDecimal ua; // A相电压
    private BigDecimal ub; // B相电压
    private BigDecimal uc; // C相电压
    private BigDecimal activePower; // 总有功功率
    private BigDecimal activeEnergy; // 总有功电能
    private BigDecimal ta;
    private BigDecimal tb;
    private BigDecimal tc;
    private BigDecimal ap;      // 总有功功率
    private BigDecimal apa;     // A相有功功率
    private BigDecimal apb;     // B相有功功率
    private BigDecimal apc;     // C相有功功率
    private BigDecimal tae;     // 总视在电能
    private BigDecimal tpf;     // 总功率因数
    private BigDecimal ae;      // 有功电能
    private BigDecimal ce;      // 修正电能
    private BigDecimal pae;     // 正向有功电能示值
    private BigDecimal rae;     // 反向有功电能示值
    private BigDecimal pre;     // 正向无功电能示值
    private BigDecimal rre;     // 反向无功电能示值
    private BigDecimal rpa;     // A相无功功率
    private BigDecimal rpb;     // B相无功功率
    private BigDecimal rpc;     // C相无功功率
    private BigDecimal pfa;     // A相功率因数
    private BigDecimal pfb;     // B相功率因数
    private BigDecimal pfc;     // C相功率因数
    private BigDecimal tre;     // 总无功电能
    private BigDecimal fr;      // 频率
    private Integer runState;  // tinyint(1)
    private String flag;
    private Integer isParsed;  // 只在 output 表中使用
} 
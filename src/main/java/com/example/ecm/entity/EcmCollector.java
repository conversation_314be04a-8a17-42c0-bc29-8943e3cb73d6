package com.example.ecm.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class EcmCollector implements Serializable {
    private Integer id;
    private BigDecimal enterpriseId;
    private String location;
    private String mn;
    private String password;
    private String sn;
    private String memo;
    private Integer createTime;
    private Integer updateTime;
} 
package com.example.ecm.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class PollutantOutputPoint implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    private Integer enterpriseId;
    private Integer collectorId;
    private Integer productionProcessId;
    private Integer pollutantOutputId;
    private Integer operatingModeId;
    private Integer processTypeGroupId;
    private Integer processTypeId;
    private Integer deviceTypeId;
    private String deviceTypeSerialId;
    private BigDecimal loadThreshold;
    private BigDecimal powerThreshold;
    private String description;
    private Integer sn;
    private String memo;
    private Integer status;
    private Integer createTime;
    private Integer updateTime;
    private String deviceCode;
    private String lastTime;

    // 电力相关字段
    private BigDecimal ia;  // A相电流
    private BigDecimal ib;  // B相电流
    private BigDecimal ic;  // C相电流
    private BigDecimal ua;  // A相电压
    private BigDecimal ub;  // B相电压
    private BigDecimal uc;  // C相电压
    private BigDecimal activePower;   // 总有功功率
    private BigDecimal activeEnergy;  // 总正向有功电能
}
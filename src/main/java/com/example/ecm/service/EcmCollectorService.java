package com.example.ecm.service;

import com.example.ecm.entity.EcmCollector;
import com.example.ecm.mapper.EcmCollectorMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class EcmCollectorService {
    
    @Resource
    private RedisCacheService redisCacheService;

    public EcmCollector getCollectorByMn(String mn) {
        // 先从Redis获取
        EcmCollector collector = redisCacheService.getCollectorByMn(mn);
        return collector;
    }
} 
package com.example.ecm.service;

import com.example.ecm.entity.EcmCollector;
import com.example.ecm.entity.PollutantOutputPoint;
import com.example.ecm.entity.PollutantTreatmentPoint;
import com.example.ecm.mapper.EcmCollectorMapper;
import com.example.ecm.mapper.PollutantOutputPointMapper;
import com.example.ecm.mapper.PollutantTreatmentPointMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RedisCacheService {
    private static final String COLLECTOR_KEY = "collector:mn:";
    private static final String OUTPUT_POINT_KEY = "output_point:enterprise_id:%s:device_code:%s";
    private static final String TREATMENT_POINT_KEY = "treatment_point:enterprise_id:%s:device_code:%s";
    
    // 缓存key的前缀模式，用于批量删除
    private static final String COLLECTOR_KEY_PATTERN = "collector:mn:*";
    private static final String OUTPUT_POINT_KEY_PATTERN = "output_point:*";
    private static final String TREATMENT_POINT_KEY_PATTERN = "treatment_point:*";

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Resource
    private EcmCollectorMapper collectorMapper;
    
    @Resource
    private PollutantOutputPointMapper outputPointMapper;
    
    @Resource
    private PollutantTreatmentPointMapper treatmentPointMapper;

    public void refreshCache() {
        log.info("开始更新Redis缓存...");
        
        try {
            // 先清除所有旧的缓存数据
            clearCache();
            
            // 刷新采集器缓存
            List<EcmCollector> collectors = collectorMapper.findAll();
            for (EcmCollector collector : collectors) {
                setCollector(collector);
            }
            log.info("数据采集器缓存刷新完成，共{}条", collectors.size());

            // 刷新排放点缓存
            List<PollutantOutputPoint> outputPoints = outputPointMapper.findAll();
            for (PollutantOutputPoint point : outputPoints) {
                if (point.getDeviceCode() != null) {
                    setOutputPoint(point.getEnterpriseId().toString(), 
                                 point.getDeviceCode(), 
                                 point);
                }
            }
            log.info("产污设施监测点缓存刷新完成，共{}条", outputPoints.size());

            // 刷新治污设施缓存
            List<PollutantTreatmentPoint> treatmentPoints = treatmentPointMapper.findAll();
            for (PollutantTreatmentPoint point : treatmentPoints) {
                if (point.getDeviceCode() != null) {
                    setTreatmentPoint(point.getEnterpriseId().toString(), 
                                    point.getDeviceCode(), 
                                    point);
                }
            }
            log.info("治污设施监测点缓存刷新完成，共{}条", treatmentPoints.size());
            
            log.info("Redis缓存更新完成");
        } catch (Exception e) {
            log.error("刷新缓存失败", e);
            throw e;
        }
    }

    private void clearCache() {
        try {
            // 删除采集器缓存
            Set<String> collectorKeys = redisTemplate.keys(COLLECTOR_KEY_PATTERN);
            if (collectorKeys != null && !collectorKeys.isEmpty()) {
                redisTemplate.delete(collectorKeys);
                log.info("清除采集器缓存完成，共{}条", collectorKeys.size());
            }

            // 删除排放点缓存
            Set<String> outputKeys = redisTemplate.keys(OUTPUT_POINT_KEY_PATTERN);
            if (outputKeys != null && !outputKeys.isEmpty()) {
                redisTemplate.delete(outputKeys);
                log.info("清除排放点缓存完成，共{}条", outputKeys.size());
            }

            // 删除治污设施缓存
            Set<String> treatmentKeys = redisTemplate.keys(TREATMENT_POINT_KEY_PATTERN);
            if (treatmentKeys != null && !treatmentKeys.isEmpty()) {
                redisTemplate.delete(treatmentKeys);
                log.info("清除治污设施缓存完成，共{}条", treatmentKeys.size());
            }
        } catch (Exception e) {
            log.error("清除缓存失败", e);
            throw e;
        }
    }

    public void setCollector(EcmCollector collector) {
        try {
            String key = COLLECTOR_KEY + collector.getMn();
            String value = objectMapper.writeValueAsString(collector);
            redisTemplate.opsForValue().set(key, value);
        } catch (Exception e) {
            log.error("缓存采集器信息失败", e);
        }
    }

    public EcmCollector getCollectorByMn(String mn) {
        try {
            String key = COLLECTOR_KEY + mn;
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                return objectMapper.readValue(value, EcmCollector.class);
            }
        } catch (Exception e) {
            log.error("获取采集器缓存失败", e);
        }
        return null;
    }

    public void setOutputPoint(String enterpriseId, String deviceCode, PollutantOutputPoint point) {
        try {
            String key = String.format(OUTPUT_POINT_KEY, enterpriseId, deviceCode);
            String value = objectMapper.writeValueAsString(point);
            redisTemplate.opsForValue().set(key, value);
            log.debug("缓存排放点信息 - 企业ID: {}, 设备编码: {}", enterpriseId, deviceCode);
        } catch (Exception e) {
            log.error("缓存排放点信息失败", e);
        }
    }

    public PollutantOutputPoint getOutputPoint(String enterpriseId, String deviceCode) {
        try {
            String key = String.format(OUTPUT_POINT_KEY, enterpriseId, deviceCode);
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                log.debug("获取排放点缓存 - 企业ID: {}, 设备编码: {}", enterpriseId, deviceCode);
                return objectMapper.readValue(value, PollutantOutputPoint.class);
            }
        } catch (Exception e) {
            log.error("获取排放点缓存失败", e);
        }
        return null;
    }

    public void setTreatmentPoint(String enterpriseId, String deviceCode, PollutantTreatmentPoint point) {
        try {
            String key = String.format(TREATMENT_POINT_KEY, enterpriseId, deviceCode);
            String value = objectMapper.writeValueAsString(point);
            redisTemplate.opsForValue().set(key, value);
            log.debug("缓存治污设施信息 - 企业ID: {}, 设备编码: {}", enterpriseId, deviceCode);
        } catch (Exception e) {
            log.error("缓存治污设施信息失败", e);
        }
    }

    public PollutantTreatmentPoint getTreatmentPoint(String enterpriseId, String deviceCode) {
        try {
            String key = String.format(TREATMENT_POINT_KEY, enterpriseId, deviceCode);
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                log.debug("获取治污设施缓存 - 企业ID: {}, 设备编码: {}", enterpriseId, deviceCode);
                return objectMapper.readValue(value, PollutantTreatmentPoint.class);
            }
        } catch (Exception e) {
            log.error("获取治污设施缓存失败", e);
        }
        return null;
    }
} 
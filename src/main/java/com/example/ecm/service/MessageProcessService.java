package com.example.ecm.service;

import com.example.ecm.entity.EcmCollector;
import com.example.ecm.entity.HisdatPoint;
import com.example.ecm.entity.PollutantOutputPoint;
import com.example.ecm.entity.PollutantTreatmentPoint;
import com.example.ecm.mapper.HisdatPointMapper;
import com.example.ecm.mapper.PollutantOutputPointMapper;
import com.example.ecm.mapper.PollutantTreatmentPointMapper;
import com.example.ecm.model.Message;
import com.example.ecm.util.MessageValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;

@Slf4j
@Service
public class MessageProcessService {

    @Resource
    private EcmCollectorService collectorService;

    @Resource
    private RedisCacheService cacheService;

    @Resource
    private HisdatPointMapper hisdatPointMapper;

    @Resource
    private MessageLogService messageLogService;

    @Resource
    private PollutantOutputPointMapper outputPointMapper;

    @Resource
    private PollutantTreatmentPointMapper treatmentPointMapper;

    public String processMessage(Message message, String originalMessage) {
        // 首先进行 HJ/T 212-2017 规范校验
        if (!validateMessage(message, originalMessage)) {
            return null;
        }

        // 根据不同的命令号处理消息
        switch (message.getCn()) {
            case "1013":
                return processInitRequest(message);
            case "9011":
            case "9012":
                // 虽然不需要处理，但已经通过了校验
                return null;
            case "2011":
                return processMonitorData(message);
            default:
                log.warn("未知的命令号: {}", message.getCn());
                return null;
        }
    }

    private boolean validateMessage(Message message, String originalMessage) {
        // 1. 基本消息校验
        if (message == null) {
            log.warn("消息为空");
            return false;
        }

        // 2. HJ/T 212-2017 规范校验
        if (!MessageValidator.validate(originalMessage)) {
            log.warn("报文格式校验失败: {}", originalMessage);
            return false;
        }

        // 3. 设备和密码校验
        if (!validateCollector(message)) {
            return false;
        }

        return true;
    }

    private boolean validateCollector(Message message) {
        if (message.getMn() == null || message.getPw() == null) {
            log.warn("消息缺少MN或PW字段");
            return false;
        }

        // 从Redis或数据库获取设备信息
        EcmCollector collector = collectorService.getCollectorByMn(message.getMn());
        if (collector == null) {
            log.warn("未找到设备信息: {}", message.getMn());
            return false;
        }

        // 校验密码
        if (!message.getPw().equals(collector.getPassword())) {
            log.warn("设备密码错误 - MN: {}", message.getMn());
            return false;
        }

        return true;
    }

    private String processInitRequest(Message message) {
        // 返回9013响应
        return message.generateResponse("9013");
    }

    private String processMonitorData(Message message) {
        try {
            Map<String, String> cp = message.getCp();
            String deviceCode = null;
            for (String key : cp.keySet()) {
                if (key.contains("-")) {
                    String[] parts = key.split("-");
                    if (parts.length >= 2) {
                        deviceCode = parts[0];
                        break;
                    }
                }
            }

            if (deviceCode == null) {
                log.warn("报文中未找到设备编码，完整报文内容: {}", message);
                return message.generateResponse("9014");
            }

            // 获取企业ID
            EcmCollector collector = collectorService.getCollectorByMn(message.getMn());
            if (collector == null) {
                log.warn("未找到对应的采集器信息, MN: {}", message.getMn());
                return message.generateResponse("9014");
            }
            String enterpriseId = collector.getEnterpriseId().toString();

            // 先查找排放点
            PollutantOutputPoint outputPoint = cacheService.getOutputPoint(enterpriseId, deviceCode);
            // 如果排放点没找到，查找治污设施点
            PollutantTreatmentPoint treatmentPoint = null;
            if (outputPoint == null) {
                treatmentPoint = cacheService.getTreatmentPoint(enterpriseId, deviceCode);
            }

            // 如果都没找到匹配的点位
            if (outputPoint == null && treatmentPoint == null) {
                log.warn("未找到匹配的监测点，企业ID: {}, 设备编码: {}", enterpriseId, deviceCode);
                return message.generateResponse("9014");
            }

            // 更新最后采集时间
            if (cp.containsKey("DataTime")) {
                try {
                    // 转换日期格式 yyyyMMddHHmmss -> yyyy-MM-dd HH:mm:ss
                    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String formattedTime = LocalDateTime.parse(cp.get("DataTime"), inputFormatter).format(outputFormatter);

                    // 解析数据并提取电力相关字段
                    HisdatPoint hisdatPoint = parseDataToHisdatPoint(message);

                    if (outputPoint != null) {
                        // 更新排放点表，包括LASTTIME和电力相关数据
                        outputPointMapper.updateLastTimeAndPowerData(
                            outputPoint.getId(),
                            formattedTime,
                            hisdatPoint.getIa(),
                            hisdatPoint.getIb(),
                            hisdatPoint.getIc(),
                            hisdatPoint.getUa(),
                            hisdatPoint.getUb(),
                            hisdatPoint.getUc(),
                            hisdatPoint.getActivePower(),
                            hisdatPoint.getActiveEnergy()
                        );
                        log.debug("更新排放点最后采集时间和电力数据 - ID: {}, 时间: {}", outputPoint.getId(), formattedTime);
                    } else if (treatmentPoint != null) {
                        // 更新治污设施表，包括LASTTIME和电力相关数据
                        treatmentPointMapper.updateLastTimeAndPowerData(
                            treatmentPoint.getId(),
                            formattedTime,
                            hisdatPoint.getIa(),
                            hisdatPoint.getIb(),
                            hisdatPoint.getIc(),
                            hisdatPoint.getUa(),
                            hisdatPoint.getUb(),
                            hisdatPoint.getUc(),
                            hisdatPoint.getActivePower(),
                            hisdatPoint.getActiveEnergy()
                        );
                        log.debug("更新治污设施最后采集时间和电力数据 - ID: {}, 时间: {}", treatmentPoint.getId(), formattedTime);
                    }
                } catch (Exception e) {
                    log.error("更新最后采集时间失败", e);
                }
            }

            // 解析数据并保存
            HisdatPoint hisdatPoint = parseDataToHisdatPoint(message);

            if (outputPoint != null) {
                // 保存到排放点历史数据表
                hisdatPoint.setPointId(outputPoint.getId());
                log.debug("准备插入数据到表 ecm_hisdat_{}_pollutant_output", enterpriseId);
                hisdatPointMapper.insertOutputPoint(enterpriseId, hisdatPoint);
                log.info("数据保存成功 - 排放点, 企业ID: {}, 设备编码: {}, 点位ID: {}",
                        enterpriseId, deviceCode, outputPoint.getId());
            } else {
                // 保存到治污设施历史数据表
                hisdatPoint.setPointId(treatmentPoint.getId());
                hisdatPointMapper.insertTreatmentPoint(enterpriseId, hisdatPoint);
                log.info("数据保存成功 - 治污设施, 企业ID: {}, 设备编码: {}, 点位ID: {}",
                        enterpriseId, deviceCode, treatmentPoint.getId());
            }

            return message.generateResponse("9014");

        } catch (Exception e) {
            log.error("处理数据报文时发生错误: {}", e.getMessage(), e);
        }
        return null;
    }

    private HisdatPoint parseDataToHisdatPoint(Message message) {
        HisdatPoint point = new HisdatPoint();
        Map<String, String> cp = message.getCp();

        // 设置时间
        try {
            // 解析报文中的时间
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            LocalDateTime dateTime = LocalDateTime.parse(cp.get("DataTime"), inputFormatter);

            // 转换为标准格式的字符串
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = dateTime.format(outputFormatter);

            // 设置时间
            point.setSampDateTime(java.sql.Timestamp.valueOf(formattedDateTime));
            point.setRcvDateTime(new Date());
            point.setInsertDateTime(new Date());

            log.debug("采样时间: {}", formattedDateTime);
        } catch (Exception e) {
            log.error("解析采样时间出错: {}", cp.get("DataTime"), e);
        }

        // 设置默认值，避免null
        point.setIa(BigDecimal.ZERO);
        point.setIb(BigDecimal.ZERO);
        point.setIc(BigDecimal.ZERO);
        point.setUa(BigDecimal.ZERO);
        point.setUb(BigDecimal.ZERO);
        point.setUc(BigDecimal.ZERO);
        point.setActivePower(BigDecimal.ZERO);
        point.setActiveEnergy(BigDecimal.ZERO);
        point.setTa(BigDecimal.ZERO);
        point.setTb(BigDecimal.ZERO);
        point.setTc(BigDecimal.ZERO);
        point.setAp(BigDecimal.ZERO);
        point.setApa(BigDecimal.ZERO);
        point.setApb(BigDecimal.ZERO);
        point.setApc(BigDecimal.ZERO);
        point.setTae(BigDecimal.ZERO);
        point.setTpf(BigDecimal.ZERO);
        point.setAe(BigDecimal.ZERO);
        point.setCe(BigDecimal.ZERO);
        point.setPae(BigDecimal.ZERO);
        point.setRae(BigDecimal.ZERO);
        point.setPre(BigDecimal.ZERO);
        point.setRre(BigDecimal.ZERO);
        point.setRpa(BigDecimal.ZERO);
        point.setRpb(BigDecimal.ZERO);
        point.setRpc(BigDecimal.ZERO);
        point.setPfa(BigDecimal.ZERO);
        point.setPfb(BigDecimal.ZERO);
        point.setPfc(BigDecimal.ZERO);
        point.setTre(BigDecimal.ZERO);
        point.setFr(BigDecimal.ZERO);
        point.setFlag("N");

        // 解析数据
        for (Map.Entry<String, String> entry : cp.entrySet()) {
            String key = entry.getKey();
            if (key.contains("-")) {
                String[] parts = key.split("-");
                if (parts.length >= 2) {
                    String field = parts[1].toLowerCase();
                    String value = entry.getValue();

                    try {
                        // 不是Flag字段才转换为BigDecimal
                        if (!"flag".equals(field)) {
                            BigDecimal decimalValue = new BigDecimal(value);
                            switch (field) {
                                case "ia":
                                    point.setIa(decimalValue);
                                    break;
                                case "ib":
                                    point.setIb(decimalValue);
                                    break;
                                case "ic":
                                    point.setIc(decimalValue);
                                    break;
                                case "ua":
                                    point.setUa(decimalValue);
                                    break;
                                case "ub":
                                    point.setUb(decimalValue);
                                    break;
                                case "uc":
                                    point.setUc(decimalValue);
                                    break;
                                case "p":
                                    point.setActivePower(decimalValue);
                                    break;
                                case "pv":
                                    point.setActiveEnergy(decimalValue);
                                    break;
                                case "ta":
                                    point.setTa(decimalValue);
                                    break;
                                case "tb":
                                    point.setTb(decimalValue);
                                    break;
                                case "tc":
                                    point.setTc(decimalValue);
                                    break;
                                case "ap":
                                    point.setAp(decimalValue);
                                    point.setActivePower(decimalValue);
                                    break;
                                case "apa":
                                    point.setApa(decimalValue);
                                    break;
                                case "apb":
                                    point.setApb(decimalValue);
                                    break;
                                case "apc":
                                    point.setApc(decimalValue);
                                    break;
                                case "tae":
                                    point.setTae(decimalValue);
                                    point.setActiveEnergy(decimalValue);
                                    break;
                                case "tpf":
                                    point.setTpf(decimalValue);
                                    break;
                                case "ae":
                                    point.setAe(decimalValue);
                                    break;
                                case "ce":
                                    point.setCe(decimalValue);
                                    break;
                                case "pae":
                                    point.setPae(decimalValue);
                                    break;
                                case "rae":
                                    point.setRae(decimalValue);
                                    break;
                                case "pre":
                                    point.setPre(decimalValue);
                                    break;
                                case "rre":
                                    point.setRre(decimalValue);
                                    break;
                                case "rpa":
                                    point.setRpa(decimalValue);
                                    break;
                                case "rpb":
                                    point.setRpb(decimalValue);
                                    break;
                                case "rpc":
                                    point.setRpc(decimalValue);
                                    break;
                                case "pfa":
                                    point.setPfa(decimalValue);
                                    break;
                                case "pfb":
                                    point.setPfb(decimalValue);
                                    break;
                                case "pfc":
                                    point.setPfc(decimalValue);
                                    break;
                                case "tre":
                                    point.setTre(decimalValue);
                                    break;
                                case "fr":
                                    point.setFr(decimalValue);
                                    break;
                            }
                        } else if ("flag".equals(field)) {
                                point.setFlag(value);
                        }
                    } catch (NumberFormatException e) {
                        log.error("解析字段值时出错，字段: {}, 值: {}", field, value, e);
                    }

                }
            }
        }

        point.setRunState(Integer.parseInt(cp.getOrDefault("RunState", "0")));
        point.setIsParsed(0);  // 默认未解析


        return point;
    }

}
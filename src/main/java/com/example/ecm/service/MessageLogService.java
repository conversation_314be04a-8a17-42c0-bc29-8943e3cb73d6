package com.example.ecm.service;

import com.example.ecm.model.Message;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MessageLogService {

    private static final Logger messageLogger = LoggerFactory.getLogger("message");

    private static final String RECEIVED_PREFIX = "收到消息";
    private static final String RESPONSE_PREFIX = "返回消息";

    public void logReceivedMessage(String message) {
        try {
            Message msg = Message.parse(message);
            if (msg == null) {
                log.warn("无法解析的消息: {}", message);
                return;
            }

            String logPrefix = RECEIVED_PREFIX;
            String logContent = "";

            switch (msg.getCn()) {
                case "1013":
                    logContent = "上传请求对时";
                    break;
                case "9011":
                    logContent = "上传收到对时数据";
                    break;
                case "9012":
                    logContent = "上传现场机设置时间完毕";
                    break;
                case "2011":
                    logContent = "上传生产监测分钟数据";
                    break;
                default:
                    logContent = "未知类型消息";
                    break;
            }

            String logMessage = String.format("%s - %s: %s", logPrefix, logContent, message);
            log.info(logMessage);
            messageLogger.info(logMessage);

        } catch (Exception e) {
            log.error("记录接收消息日志失败", e);
        }
    }

    public void logResponseMessage(String message) {
        try {
            Message msg = Message.parse(message);
            if (msg == null) {
                log.warn("无法解析的响应消息: {}", message);
                return;
            }

            String logPrefix = RESPONSE_PREFIX;
            String logContent = "";

            switch (msg.getCn()) {
                case "9013":
                    logContent = "返回收到对时请求";
                    break;
                case "1012":
                    logContent = "返回平台时间";
                    break;
                case "9014":
                    logContent = "返回数据应答";
                    break;
                default:
                    logContent = "未知类型响应";
                    break;
            }

            String logMessage = String.format("%s - %s: %s", logPrefix, logContent, message);
            log.info(logMessage);
            messageLogger.info(logMessage);

        } catch (Exception e) {
            log.error("记录响应消息日志失败", e);
        }
    }
}
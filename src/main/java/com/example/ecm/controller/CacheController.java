package com.example.ecm.controller;

import com.example.ecm.service.RedisCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/cache")
public class CacheController {

    @Resource
    private RedisCacheService redisCacheService;

    /**
     * 刷新redis缓存
     * @return
     */
    @PostMapping("/refresh")
    public Map<String, Object> refreshCache() {
        Map<String, Object> result = new HashMap<>();
        try {
            redisCacheService.refreshCache();
            result.put("success", true);
            result.put("message", "缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新缓存失败", e);
            result.put("success", false);
            result.put("message", "缓存刷新失败: " + e.getMessage());
        }
        return result;
    }
} 
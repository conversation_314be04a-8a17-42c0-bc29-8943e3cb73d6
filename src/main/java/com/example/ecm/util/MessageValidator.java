package com.example.ecm.util;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MessageValidator {
    private static final String MESSAGE_PREFIX = "##";
    
    public static boolean validate(String message) {
        if (message == null || !message.startsWith(MESSAGE_PREFIX)) {
            log.warn("报文格式错误，缺少##前缀: {}", message);
            return false;
        }
        
        try {
            // 标准化换行符，移除可能的回车换行
            message = message.replaceAll("[\r\n]", "").trim();
            
            // 提取并验证数据段长度
            String lengthStr = message.substring(2, 6);
            int declaredLength = Integer.parseInt(lengthStr);
            
            // 先提取原始数据段（不包含##和CRC校验码）
            String originalData = message.substring(6, message.length() - 4);
            
            // 先检查原始数据长度
            if (originalData.length() != declaredLength) {
                log.warn("数据段长度不匹配 - 声明: {}, 实际: {}, 原始数据: {}", 
                    declaredLength, originalData.length(), originalData);
                return false;
            }
            
            // 标准化分隔符（将:替换为;）用于后续处理
            String normalizedData = originalData.replace(':', ';');
            
            // 检查CRC
            String declaredCrc = message.substring(message.length() - 4);
            String calculatedCrc = calculateCRC(normalizedData);
            
            if (!declaredCrc.equalsIgnoreCase(calculatedCrc)) {
                log.warn("CRC校验失败 - 声明: {}, 计算: {}", declaredCrc, calculatedCrc);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("报文验证异常: {}", message, e);
            return false;
        }
    }
    
    public static String calculateCRC(String data) {
        try {
            byte[] bytes = data.getBytes("UTF-8");
            int crc_reg = 0xFFFF;  // 初始值
            
            for (int i = 0; i < bytes.length; i++) {
                crc_reg = (crc_reg >> 8) ^ (bytes[i] & 0xFF);
                for (int j = 0; j < 8; j++) {
                    int check = crc_reg & 0x0001;
                    crc_reg >>= 1;
                    if (check == 0x0001) {
                        crc_reg ^= 0xA001;
                    }
                }
            }
            
            // 转换为4位十六进制，高字节在前
            return String.format("%04X", crc_reg).toUpperCase();
            
        } catch (Exception e) {
            log.error("CRC计算异常", e);
            return "0000";
        }
    }
    
    public static String extractData(String message) {
        try {
            // 提取数据段（不包含CRC校验码）
            String data = message.substring(6, message.length() - 4);
            
            // 找到最后一个&&的位置
            int lastIndex = data.lastIndexOf("&&");
            if (lastIndex > 0) {
                // 重新截取数据段
                return data.substring(0, lastIndex + 2);
            }
            
            return data;
        } catch (Exception e) {
            log.error("提取数据段失败", e);
            return null;
        }
    }
    
    public static String wrapMessage(String data) {
        try {
            // 计算原始数据段长度（不包含##和CRC）
            String lengthStr = String.format("%04d", data.length());
            
            // 标准化分隔符用于CRC计算
            String normalizedData = data.replace(':', ';');
            
            // 计算CRC
            String crc = calculateCRC(normalizedData);
            // 拼接完整报文
            return String.format("##%s%s%s", lengthStr, data, crc);
        } catch (Exception e) {
            log.error("包装消息异常", e);
            return null;
        }
    }

    public static void main(String[] args) {
        // 测试实际收到的消息
        String message = "##0308QN=20200119152709570;ST=80;CN=9013;PW=123456;MN=SCECM0871AN2021060100001;Flag=5;CP=&&DataTime=20240228152706;ay01-Ia=82.184,ay01-AP=11.184,ay01-APA=13.184,ay01-APB=12.184,ay01-APc=12.184,ay01-Ib=87.299,ay01-Ic=85.478,ay01-Ua=233.651,ay01-Ub=236.353,ay01-Uc=233.925,ay01-P=52.620,ay01-Pv=111.020,ay01-Flag=N&&F781";
        
        // 提取数据段
        String data = message.substring(6, message.length() - 4);
        // 计算CRC
        String calculatedCrc = calculateCRC(data);
        
        log.info("数据段: {}", data);
        log.info("数据段长度: {}", data.length());
        log.info("声明的CRC: {}", message.substring(message.length() - 4));
        log.info("计算的CRC: {}", calculatedCrc);
        log.info("验证结果: {}", validate(message));
    }
} 
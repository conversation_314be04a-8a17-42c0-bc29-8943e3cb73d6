package com.example.ecm;

import com.example.ecm.service.RedisCacheService;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
@MapperScan("com.example.ecm.mapper")
public class EcmDataCollectorApplication {
    public static void main(String[] args) {
        SpringApplication.run(EcmDataCollectorApplication.class, args);
    }

    @Bean
    public CommandLineRunner initCache(RedisCacheService redisCacheService) {
        return args -> {
            redisCacheService.refreshCache();
        };
    }
} 
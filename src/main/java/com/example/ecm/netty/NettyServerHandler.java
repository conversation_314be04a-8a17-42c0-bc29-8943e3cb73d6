package com.example.ecm.netty;

import com.example.ecm.model.Message;
import com.example.ecm.service.MessageLogService;
import com.example.ecm.service.MessageProcessService;
import com.example.ecm.util.MessageValidator;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Component
@ChannelHandler.Sharable
public class NettyServerHandler extends SimpleChannelInboundHandler<String> {

    @Resource
    private MessageProcessService messageProcessService;

    @Resource
    private MessageLogService messageLogService;

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, String msg) {
        // 打印原始报文
        log.info("收到原始报文: {}", msg);

        // 检查消息是否为空
        if (msg == null) {
            log.warn("接收到空报文");
            return;
        }

        // 检查报文长度是否超过10个字符
        if (msg.length() <= 10) {
            log.warn("报文长度不足({}): {}", msg.length(), msg);
            return;
        }

        String trimmedMsg = msg.trim();
        if (trimmedMsg.isEmpty()) {
            log.warn("报文仅包含空白字符: {}", msg);
            return;
        }

        if (trimmedMsg.length() <= 10) {
            log.warn("报文去除空白后长度不足({}): {}", trimmedMsg.length(), msg);
            return;
        }

        messageLogService.logReceivedMessage(msg);
        try {
            Message message = Message.parse(msg);
            if (message == null) {
                log.warn("报文解析失败: {}", msg);
                return;
            }

            String response = messageProcessService.processMessage(message, msg);

            if (response != null) {
                ctx.writeAndFlush(response);
                messageLogService.logResponseMessage(response);
                // 如果是初始化报文(1013)，还需要发送时间请求报文
                String cn = message.getCn();
                if (cn != null && "1013".equals(cn)) {
                    // 构建时间请求报文数据段
                    String timeRequestData = buildTimeRequestData(message);
                    // 包装成完整的协议报文
                    String timeRequest = MessageValidator.wrapMessage(timeRequestData);

                    ctx.writeAndFlush(timeRequest);
                    messageLogService.logResponseMessage(timeRequest);
                }
            }
        } catch (Exception e) {
            log.error("处理消息时发生错误", e);
        }
    }

    private String buildTimeRequestData(Message originalMessage) {
        if (originalMessage == null) {
            log.warn("原始消息为空，无法构建时间请求");
            return null;
        }

        StringBuilder data = new StringBuilder();
        // 使用新的QN
        String newQn = generateNewQn();
        data.append("QN=").append(newQn).append(";");
        data.append("ST=80;");
        data.append("CN=1012;");
        // 防止pw属性为空导致的空指针异常
        String pw = originalMessage.getPw();
        data.append("PW=").append(pw != null ? pw : "").append(";");

        // 防止mn属性为空导致的空指针异常
        String mn = originalMessage.getMn();
        data.append("MN=").append(mn != null ? mn : "").append(";");
        data.append("Flag=5;");
        data.append("CP=&&SystemTime=").append(getCurrentTime()).append("&&");
        return data.toString();
    }

    private String generateNewQn() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
    }

    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        log.info("Client connected: {}", ctx.channel().remoteAddress());
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        log.info("Client disconnected: {}", ctx.channel().remoteAddress());
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("Channel exception caught", cause);
        ctx.close();
    }
} 
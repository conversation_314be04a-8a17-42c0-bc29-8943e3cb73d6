package com.example.ecm.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;
import com.example.ecm.util.MessageValidator;

@Data
@Slf4j
public class Message {
    private String qn;
    private String st;
    private String cn;
    private String pw;
    private String mn;
    private String flag;
    private Map<String, String> cp;

    public static Message parse(String message) {
        try {
            Message result = new Message();
            // 提取数据段
            String data = MessageValidator.extractData(message);
            // 找到CP段的位置
            int cpIndex = data.indexOf("CP=");
            if (cpIndex >= 0) {
                // 处理CP段之前的内容
                String beforeCp = data.substring(0, cpIndex);
                String[] fields = beforeCp.split(";");
                
                // 解析主要字段
                for (String field : fields) {
                    if (field.startsWith("QN=")) {
                        result.setQn(field.substring(3));
                    } else if (field.startsWith("ST=")) {
                        result.setSt(field.substring(3));
                    } else if (field.startsWith("CN=")) {
                        result.setCn(field.substring(3));
                    } else if (field.startsWith("PW=")) {
                        result.setPw(field.substring(3));
                    } else if (field.startsWith("MN=")) {
                        result.setMn(field.substring(3));
                    } else if (field.startsWith("Flag=")) {
                        result.setFlag(field.substring(5));
                    }
                }
                
                // 处理CP段内容
                String cpContent = data.substring(cpIndex + 3);
                Map<String, String> cp = new HashMap<>();
                if (cpContent.startsWith("&&") && cpContent.endsWith("&&")) {
                    // 去掉首尾的&&
                    cpContent = cpContent.substring(2, cpContent.length() - 2);
                    // 先处理分号分隔的字段
                    String[] mainFields = cpContent.split(";");
                    for (String mainField : mainFields) {
                        // 处理每个主字段中的逗号分隔的子字段
                        String[] subFields = mainField.split(",");
                        for (String subField : subFields) {// 处理带有=号的字段
                            if (subField.contains("=")) {
                                String[] parts = subField.split("=", 2);
                                cp.put(parts[0], parts[1]);
                            }
                        }
                    }
                }
                result.setCp(cp);
            }
            return result;
        } catch (Exception e) {
            log.error("解析报文失败: {}", message, e);
            return null;
        }
    }

    public String generateResponse(String cn) {
        // 构建数据段
        StringBuilder data = new StringBuilder();
        data.append("QN=").append(this.qn).append(";");
        data.append("ST=91;");
        data.append("CN=").append(cn).append(";");
        data.append("PW=").append(this.pw).append(";");
        data.append("MN=").append(this.mn).append(";");
        data.append("Flag=4;");
        data.append("CP=&&&&");
        
        // 包装成完整的HJ/T 212-2017格式报文
        return MessageValidator.wrapMessage(data.toString());
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.ecm.mapper.HisdatPointMapper">

    <insert id="insertOutputPoint">
        MERGE INTO ecm_hisdat_${enterpriseId}_pollutant_output t
        USING (SELECT
        #{point.pointId} as POINT_ID,
        #{point.sampDateTime} as SAMP_DATETIME,
        #{point.rcvDateTime} as RCV_DATETIME,
        #{point.insertDateTime} as INSERT_DATETIME,
        #{point.ia, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as IA,
        #{point.ib, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as IB,
        #{point.ic, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as IC,
        #{point.ua, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as UA,
        #{point.ub, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as UB,
        #{point.uc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as UC,
        #{point.activePower, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as ACTIVE_POWER,
        #{point.activeEnergy, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as ACTIVE_ENERGY,
        #{point.ta, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TA,
        #{point.tb, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TB,
        #{point.tc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TC,
        #{point.runState, jdbcType=INTEGER} as RUN_STATE,
        #{point.flag, jdbcType=VARCHAR} as FLAG,
        #{point.ap, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as AP,
        #{point.apa, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as APA,
        #{point.apb, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as APB,
        #{point.apc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as APC,
        #{point.tae, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TAE,
        #{point.tpf, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TPF,
        #{point.ae, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as AE,
        #{point.ce, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as CE,
        #{point.pae, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PAE,
        #{point.rae, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RAE,
        #{point.pre, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PRE,
        #{point.rre, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RRE,
        #{point.rpa, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RPA,
        #{point.rpb, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RPB,
        #{point.rpc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RPC,
        #{point.pfa, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PFA,
        #{point.pfb, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PFB,
        #{point.pfc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PFC,
        #{point.tre, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TRE,
        #{point.fr, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as FR
        FROM DUAL) s
        ON (t.POINT_ID = s.POINT_ID)
        WHEN MATCHED THEN
        UPDATE SET
        t.RCV_DATETIME = s.RCV_DATETIME,
        t.INSERT_DATETIME = s.INSERT_DATETIME
        <if test="point.ia != null and point.ia.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.IA = s.IA</if>
        <if test="point.ib != null and point.ib.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.IB = s.IB</if>
        <if test="point.ic != null and point.ic.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.IC = s.IC</if>
        <if test="point.ua != null and point.ua.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.UA = s.UA</if>
        <if test="point.ub != null and point.ub.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.UB = s.UB</if>
        <if test="point.uc != null and point.uc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.UC = s.UC</if>
        <if test="point.activePower != null and point.activePower.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.ACTIVE_POWER = s.ACTIVE_POWER</if>
        <if test="point.activeEnergy != null and point.activeEnergy.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.ACTIVE_ENERGY = s.ACTIVE_ENERGY</if>
        <if test="point.ta != null and point.ta.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TA = s.TA</if>
        <if test="point.tb != null and point.tb.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TB = s.TB</if>
        <if test="point.tc != null and point.tc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TC = s.TC</if>
        <if test="point.runState != null">, t.RUN_STATE = s.RUN_STATE</if>
        <if test="point.flag != null">, t.FLAG = s.FLAG</if>
        <if test="point.ap != null and point.ap.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.AP = s.AP</if>
        <if test="point.apa != null and point.apa.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.APA = s.APA</if>
        <if test="point.apb != null and point.apb.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.APB = s.APB</if>
        <if test="point.apc != null and point.apc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.APC = s.APC</if>
        <if test="point.tae != null and point.tae.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TAE = s.TAE</if>
        <if test="point.tpf != null and point.tpf.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TPF = s.TPF</if>
        <if test="point.ae != null and point.ae.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.AE = s.AE</if>
        <if test="point.ce != null and point.ce.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.CE = s.CE</if>
        <if test="point.pae != null and point.pae.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PAE = s.PAE</if>
        <if test="point.rae != null and point.rae.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RAE = s.RAE</if>
        <if test="point.pre != null and point.pre.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PRE = s.PRE</if>
        <if test="point.rre != null and point.rre.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RRE = s.RRE</if>
        <if test="point.rpa != null and point.rpa.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RPA = s.RPA</if>
        <if test="point.rpb != null and point.rpb.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RPB = s.RPB</if>
        <if test="point.rpc != null and point.rpc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RPC = s.RPC</if>
        <if test="point.pfa != null and point.pfa.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PFA = s.PFA</if>
        <if test="point.pfb != null and point.pfb.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PFB = s.PFB</if>
        <if test="point.pfc != null and point.pfc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PFC = s.PFC</if>
        <if test="point.tre != null and point.tre.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TRE = s.TRE</if>
        <if test="point.fr != null and point.fr.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.FR = s.FR</if>
        WHEN NOT MATCHED THEN
        INSERT (
        POINT_ID, SAMP_DATETIME, RCV_DATETIME, INSERT_DATETIME,
        IA, IB, IC, UA, UB, UC,
        ACTIVE_POWER, ACTIVE_ENERGY,
        TA, TB, TC, RUN_STATE, FLAG,
        AP, APA, APB, APC,
        TAE, TPF, AE, CE,
        PAE, RAE, PRE, RRE,
        RPA, RPB, RPC,
        PFA, PFB, PFC,
        TRE, FR
        )
        VALUES (
        s.POINT_ID, s.SAMP_DATETIME, s.RCV_DATETIME, s.INSERT_DATETIME,
        s.IA, s.IB, s.IC, s.UA, s.UB, s.UC,
        s.ACTIVE_POWER, s.ACTIVE_ENERGY,
        s.TA, s.TB, s.TC, s.RUN_STATE, s.FLAG,
        s.AP, s.APA, s.APB, s.APC,
        s.TAE, s.TPF, s.AE, s.CE,
        s.PAE, s.RAE, s.PRE, s.RRE,
        s.RPA, s.RPB, s.RPC,
        s.PFA, s.PFB, s.PFC,
        s.TRE, s.FR
        )
    </insert>


    <insert id="insertTreatmentPoint">
        MERGE INTO ecm_hisdat_${enterpriseId}_pollutant_treatment t
        USING (SELECT
        #{point.pointId} as POINT_ID,
        #{point.sampDateTime} as SAMP_DATETIME,
        #{point.rcvDateTime} as RCV_DATETIME,
        #{point.insertDateTime} as INSERT_DATETIME,
        #{point.ia, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as IA,
        #{point.ib, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as IB,
        #{point.ic, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as IC,
        #{point.ua, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as UA,
        #{point.ub, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as UB,
        #{point.uc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as UC,
        #{point.activePower, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as ACTIVE_POWER,
        #{point.activeEnergy, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as ACTIVE_ENERGY,
        #{point.ta, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TA,
        #{point.tb, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TB,
        #{point.tc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TC,
        #{point.runState, jdbcType=INTEGER} as RUN_STATE,
        #{point.flag, jdbcType=VARCHAR} as FLAG,
        #{point.ap, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as AP,
        #{point.apa, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as APA,
        #{point.apb, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as APB,
        #{point.apc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as APC,
        #{point.tae, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TAE,
        #{point.tpf, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TPF,
        #{point.ae, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as AE,
        #{point.ce, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as CE,
        #{point.pae, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PAE,
        #{point.rae, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RAE,
        #{point.pre, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PRE,
        #{point.rre, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RRE,
        #{point.rpa, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RPA,
        #{point.rpb, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RPB,
        #{point.rpc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as RPC,
        #{point.pfa, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PFA,
        #{point.pfb, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PFB,
        #{point.pfc, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as PFC,
        #{point.tre, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as TRE,
        #{point.fr, jdbcType=DECIMAL, javaType=java.math.BigDecimal} as FR
        FROM DUAL) s
        ON (t.POINT_ID = s.POINT_ID)
        WHEN MATCHED THEN
        UPDATE SET
        t.RCV_DATETIME = s.RCV_DATETIME,
        t.INSERT_DATETIME = s.INSERT_DATETIME
        <if test="point.ia != null and point.ia.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.IA = s.IA</if>
        <if test="point.ib != null and point.ib.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.IB = s.IB</if>
        <if test="point.ic != null and point.ic.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.IC = s.IC</if>
        <if test="point.ua != null and point.ua.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.UA = s.UA</if>
        <if test="point.ub != null and point.ub.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.UB = s.UB</if>
        <if test="point.uc != null and point.uc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.UC = s.UC</if>
        <if test="point.activePower != null and point.activePower.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.ACTIVE_POWER = s.ACTIVE_POWER</if>
        <if test="point.activeEnergy != null and point.activeEnergy.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.ACTIVE_ENERGY = s.ACTIVE_ENERGY</if>
        <if test="point.ta != null and point.ta.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TA = s.TA</if>
        <if test="point.tb != null and point.tb.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TB = s.TB</if>
        <if test="point.tc != null and point.tc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TC = s.TC</if>
        <if test="point.runState != null">, t.RUN_STATE = s.RUN_STATE</if>
        <if test="point.flag != null">, t.FLAG = s.FLAG</if>
        <if test="point.ap != null and point.ap.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.AP = s.AP</if>
        <if test="point.apa != null and point.apa.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.APA = s.APA</if>
        <if test="point.apb != null and point.apb.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.APB = s.APB</if>
        <if test="point.apc != null and point.apc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.APC = s.APC</if>
        <if test="point.tae != null and point.tae.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TAE = s.TAE</if>
        <if test="point.tpf != null and point.tpf.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TPF = s.TPF</if>
        <if test="point.ae != null and point.ae.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.AE = s.AE</if>
        <if test="point.ce != null and point.ce.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.CE = s.CE</if>
        <if test="point.pae != null and point.pae.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PAE = s.PAE</if>
        <if test="point.rae != null and point.rae.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RAE = s.RAE</if>
        <if test="point.pre != null and point.pre.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PRE = s.PRE</if>
        <if test="point.rre != null and point.rre.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RRE = s.RRE</if>
        <if test="point.rpa != null and point.rpa.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RPA = s.RPA</if>
        <if test="point.rpb != null and point.rpb.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RPB = s.RPB</if>
        <if test="point.rpc != null and point.rpc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.RPC = s.RPC</if>
        <if test="point.pfa != null and point.pfa.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PFA = s.PFA</if>
        <if test="point.pfb != null and point.pfb.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PFB = s.PFB</if>
        <if test="point.pfc != null and point.pfc.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.PFC = s.PFC</if>
        <if test="point.tre != null and point.tre.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.TRE = s.TRE</if>
        <if test="point.fr != null and point.fr.compareTo(new java.math.BigDecimal('0.0')) != 0">, t.FR = s.FR</if>
        WHEN NOT MATCHED THEN
        INSERT (
        POINT_ID, SAMP_DATETIME, RCV_DATETIME, INSERT_DATETIME,
        IA, IB, IC, UA, UB, UC,
        ACTIVE_POWER, ACTIVE_ENERGY,
        TA, TB, TC, RUN_STATE, FLAG,
        AP, APA, APB, APC,
        TAE, TPF, AE, CE,
        PAE, RAE, PRE, RRE,
        RPA, RPB, RPC,
        PFA, PFB, PFC,
        TRE, FR
        )
        VALUES (
        s.POINT_ID, s.SAMP_DATETIME, s.RCV_DATETIME, s.INSERT_DATETIME,
        s.IA, s.IB, s.IC, s.UA, s.UB, s.UC,
        s.ACTIVE_POWER, s.ACTIVE_ENERGY,
        s.TA, s.TB, s.TC, s.RUN_STATE, s.FLAG,
        s.AP, s.APA, s.APB, s.APC,
        s.TAE, s.TPF, s.AE, s.CE,
        s.PAE, s.RAE, s.PRE, s.RRE,
        s.RPA, s.RPB, s.RPC,
        s.PFA, s.PFB, s.PFC,
        s.TRE, s.FR
        )
    </insert>
</mapper>
<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">-->
<!--<mapper namespace="com.example.ecm.mapper.HisdatPointMapper">-->
<!--    -->
<!--    <insert id="insertOutputPoint">-->
<!--        &lt;!&ndash; 先获取表的列信息 &ndash;&gt;-->
<!--        <selectKey keyProperty="columns" resultType="map" order="BEFORE">-->
<!--            SELECT GROUP_CONCAT(COLUMN_NAME) as columns-->
<!--            FROM INFORMATION_SCHEMA.COLUMNS-->
<!--            WHERE TABLE_SCHEMA = DATABASE()-->
<!--            AND TABLE_NAME = CONCAT('ecm_hisdat_${enterpriseId}_pollutant_output')-->
<!--        </selectKey>-->

<!--        INSERT INTO ecm_hisdat_${enterpriseId}_pollutant_output -->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            &lt;!&ndash; 必需字段 &ndash;&gt;-->
<!--            POINT_ID, SAMP_DATETIME, RCV_DATETIME, INSERT_DATETIME,-->
<!--            <if test="columns != null and columns.contains('IA')">IA,</if>-->
<!--            <if test="columns != null and columns.contains('IB')">IB,</if>-->
<!--            <if test="columns != null and columns.contains('IC')">IC,</if>-->
<!--            <if test="columns != null and columns.contains('UA')">UA,</if>-->
<!--            <if test="columns != null and columns.contains('UB')">UB,</if>-->
<!--            <if test="columns != null and columns.contains('UC')">UC,</if>-->
<!--            <if test="columns != null and columns.contains('ACTIVE_POWER')">ACTIVE_POWER,</if>-->
<!--            <if test="columns != null and columns.contains('ACTIVE_ENERGY')">ACTIVE_ENERGY,</if>-->
<!--            <if test="columns != null and columns.contains('TA')">TA,</if>-->
<!--            <if test="columns != null and columns.contains('TB')">TB,</if>-->
<!--            <if test="columns != null and columns.contains('TC')">TC,</if>-->
<!--            <if test="columns != null and columns.contains('RUN_STATE')">RUN_STATE,</if>-->
<!--            <if test="columns != null and columns.contains('FLAG')">FLAG,</if>-->
<!--            <if test="columns != null and columns.contains('AP')">AP,</if>-->
<!--            <if test="columns != null and columns.contains('APa')">APa,</if>-->
<!--            <if test="columns != null and columns.contains('APb')">APb,</if>-->
<!--            <if test="columns != null and columns.contains('APc')">APc,</if>-->
<!--            <if test="columns != null and columns.contains('TAE')">TAE,</if>-->
<!--            <if test="columns != null and columns.contains('TPF')">TPF,</if>-->
<!--            <if test="columns != null and columns.contains('AE')">AE,</if>-->
<!--            <if test="columns != null and columns.contains('CE')">CE,</if>-->
<!--            <if test="columns != null and columns.contains('PAE')">PAE,</if>-->
<!--            <if test="columns != null and columns.contains('RAE')">RAE,</if>-->
<!--            <if test="columns != null and columns.contains('PRE')">PRE,</if>-->
<!--            <if test="columns != null and columns.contains('RRE')">RRE,</if>-->
<!--            <if test="columns != null and columns.contains('RPa')">RPa,</if>-->
<!--            <if test="columns != null and columns.contains('RPb')">RPb,</if>-->
<!--            <if test="columns != null and columns.contains('RPc')">RPc,</if>-->
<!--            <if test="columns != null and columns.contains('PFa')">PFa,</if>-->
<!--            <if test="columns != null and columns.contains('PFb')">PFb,</if>-->
<!--            <if test="columns != null and columns.contains('PFc')">PFc,</if>-->
<!--            <if test="columns != null and columns.contains('TRE')">TRE,</if>-->
<!--            <if test="columns != null and columns.contains('Fr')">FR</if>-->
<!--        </trim>-->
<!--        VALUES -->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            #{point.pointId}, #{point.sampDateTime}, #{point.rcvDateTime}, #{point.insertDateTime},-->
<!--            <if test="columns != null and columns.contains('IA')">#{point.ia, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('IB')">#{point.ib, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('IC')">#{point.ic, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('UA')">#{point.ua, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('UB')">#{point.ub, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('UC')">#{point.uc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('ACTIVE_POWER')">#{point.activePower, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('ACTIVE_ENERGY')">#{point.activeEnergy, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TA')">#{point.ta, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TB')">#{point.tb, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TC')">#{point.tc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RUN_STATE')">#{point.runState, jdbcType=INTEGER},</if>-->
<!--            <if test="columns != null and columns.contains('FLAG')">#{point.flag, jdbcType=VARCHAR},</if>-->
<!--            <if test="columns != null and columns.contains('AP')">#{point.ap, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('APa')">#{point.apa, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('APb')">#{point.apb, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('APc')">#{point.apc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TAE')">#{point.tae, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TPF')">#{point.tpf, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('AE')">#{point.ae, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('CE')">#{point.ce, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PAE')">#{point.pae, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RAE')">#{point.rae, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PRE')">#{point.pre, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RRE')">#{point.rre, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RPa')">#{point.rpa, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RPb')">#{point.rpb, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RPc')">#{point.rpc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PFa')">#{point.pfa, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PFb')">#{point.pfb, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PFc')">#{point.pfc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TRE')">#{point.tre, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('Fr')">#{point.fr, jdbcType=DECIMAL, javaType=java.math.BigDecimal}</if>-->
<!--        </trim>-->
<!--        ON DUPLICATE KEY UPDATE-->
<!--        RCV_DATETIME = VALUES(RCV_DATETIME),-->
<!--        INSERT_DATETIME = VALUES(INSERT_DATETIME)-->
<!--        &lt;!&ndash; 更新时只更新非0值 &ndash;&gt;-->
<!--        <if test="point.ia != null and point.ia.compareTo(new java.math.BigDecimal('0.0')) != 0">, IA = VALUES(IA)</if>-->
<!--        <if test="point.ib != null and point.ib.compareTo(new java.math.BigDecimal('0.0')) != 0">, IB = VALUES(IB)</if>-->
<!--        <if test="point.ic != null and point.ic.compareTo(new java.math.BigDecimal('0.0')) != 0">, IC = VALUES(IC)</if>-->
<!--        <if test="point.ua != null and point.ua.compareTo(new java.math.BigDecimal('0.0')) != 0">, UA = VALUES(UA)</if>-->
<!--        <if test="point.ub != null and point.ub.compareTo(new java.math.BigDecimal('0.0')) != 0">, UB = VALUES(UB)</if>-->
<!--        <if test="point.uc != null and point.uc.compareTo(new java.math.BigDecimal('0.0')) != 0">, UC = VALUES(UC)</if>-->
<!--        <if test="point.activePower != null and point.activePower.compareTo(new java.math.BigDecimal('0.0')) != 0">, ACTIVE_POWER = VALUES(ACTIVE_POWER)</if>-->
<!--        <if test="point.activeEnergy != null and point.activeEnergy.compareTo(new java.math.BigDecimal('0.0')) != 0">, ACTIVE_ENERGY = VALUES(ACTIVE_ENERGY)</if>-->
<!--        <if test="point.ta != null and point.ta.compareTo(new java.math.BigDecimal('0.0')) != 0">, TA = VALUES(TA)</if>-->
<!--        <if test="point.tb != null and point.tb.compareTo(new java.math.BigDecimal('0.0')) != 0">, TB = VALUES(TB)</if>-->
<!--        <if test="point.tc != null and point.tc.compareTo(new java.math.BigDecimal('0.0')) != 0">, TC = VALUES(TC)</if>-->
<!--        <if test="point.runState != null">, RUN_STATE = VALUES(RUN_STATE)</if>-->
<!--        <if test="point.flag != null">, FLAG = VALUES(FLAG)</if>-->
<!--        <if test="point.ap != null and point.ap.compareTo(new java.math.BigDecimal('0.0')) != 0">, AP = VALUES(AP)</if>-->
<!--        <if test="point.apa != null and point.apa.compareTo(new java.math.BigDecimal('0.0')) != 0">, APA = VALUES(APA)</if>-->
<!--        <if test="point.apb != null and point.apb.compareTo(new java.math.BigDecimal('0.0')) != 0">, APB = VALUES(APB)</if>-->
<!--        <if test="point.apc != null and point.apc.compareTo(new java.math.BigDecimal('0.0')) != 0">, APC = VALUES(APC)</if>-->
<!--        <if test="point.tae != null and point.tae.compareTo(new java.math.BigDecimal('0.0')) != 0">, TAE = VALUES(TAE)</if>-->
<!--        <if test="point.tpf != null and point.tpf.compareTo(new java.math.BigDecimal('0.0')) != 0">, TPF = VALUES(TPF)</if>-->
<!--        <if test="point.ae != null and point.ae.compareTo(new java.math.BigDecimal('0.0')) != 0">, AE = VALUES(AE)</if>-->
<!--        <if test="point.ce != null and point.ce.compareTo(new java.math.BigDecimal('0.0')) != 0">, CE = VALUES(CE)</if>-->
<!--        <if test="point.pae != null and point.pae.compareTo(new java.math.BigDecimal('0.0')) != 0">, PAE = VALUES(PAE)</if>-->
<!--        <if test="point.rae != null and point.rae.compareTo(new java.math.BigDecimal('0.0')) != 0">, RAE = VALUES(RAE)</if>-->
<!--        <if test="point.pre != null and point.pre.compareTo(new java.math.BigDecimal('0.0')) != 0">, PRE = VALUES(PRE)</if>-->
<!--        <if test="point.rre != null and point.rre.compareTo(new java.math.BigDecimal('0.0')) != 0">, RRE = VALUES(RRE)</if>-->
<!--        <if test="point.rpa != null and point.rpa.compareTo(new java.math.BigDecimal('0.0')) != 0">, RPA = VALUES(RPA)</if>-->
<!--        <if test="point.rpb != null and point.rpb.compareTo(new java.math.BigDecimal('0.0')) != 0">, RPB = VALUES(RPB)</if>-->
<!--        <if test="point.rpc != null and point.rpc.compareTo(new java.math.BigDecimal('0.0')) != 0">, RPC = VALUES(RPC)</if>-->
<!--        <if test="point.pfa != null and point.pfa.compareTo(new java.math.BigDecimal('0.0')) != 0">, PFA = VALUES(PFA)</if>-->
<!--        <if test="point.pfb != null and point.pfb.compareTo(new java.math.BigDecimal('0.0')) != 0">, PFB = VALUES(PFB)</if>-->
<!--        <if test="point.pfc != null and point.pfc.compareTo(new java.math.BigDecimal('0.0')) != 0">, PFC = VALUES(PFC)</if>-->
<!--        <if test="point.tre != null and point.tre.compareTo(new java.math.BigDecimal('0.0')) != 0">, TRE = VALUES(TRE)</if>-->
<!--        <if test="point.fr != null and point.fr.compareTo(new java.math.BigDecimal('0.0')) != 0">, FR = VALUES(FR)</if>-->
<!--    </insert>-->


<!--    <insert id="insertTreatmentPoint">-->

<!--        <selectKey keyProperty="columns" resultType="map" order="BEFORE">-->
<!--            SELECT GROUP_CONCAT(COLUMN_NAME) as columns-->
<!--            FROM INFORMATION_SCHEMA.COLUMNS-->
<!--            WHERE TABLE_SCHEMA = DATABASE()-->
<!--            AND TABLE_NAME = CONCAT('ecm_hisdat_${enterpriseId}_pollutant_treatment')-->
<!--        </selectKey>-->

<!--        INSERT INTO ecm_hisdat_${enterpriseId}_pollutant_treatment-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            POINT_ID, SAMP_DATETIME, RCV_DATETIME, INSERT_DATETIME,-->
<!--            <if test="columns != null and columns.contains('IA')">IA,</if>-->
<!--            <if test="columns != null and columns.contains('IB')">IB,</if>-->
<!--            <if test="columns != null and columns.contains('IC')">IC,</if>-->
<!--            <if test="columns != null and columns.contains('UA')">UA,</if>-->
<!--            <if test="columns != null and columns.contains('UB')">UB,</if>-->
<!--            <if test="columns != null and columns.contains('UC')">UC,</if>-->
<!--            <if test="columns != null and columns.contains('ACTIVE_POWER')">ACTIVE_POWER,</if>-->
<!--            <if test="columns != null and columns.contains('ACTIVE_ENERGY')">ACTIVE_ENERGY,</if>-->
<!--            <if test="columns != null and columns.contains('TA')">TA,</if>-->
<!--            <if test="columns != null and columns.contains('TB')">TB,</if>-->
<!--            <if test="columns != null and columns.contains('TC')">TC,</if>-->
<!--            <if test="columns != null and columns.contains('RUN_STATE')">RUN_STATE,</if>-->
<!--            <if test="columns != null and columns.contains('FLAG')">FLAG,</if>-->
<!--            <if test="columns != null and columns.contains('AP')">AP,</if>-->
<!--            <if test="columns != null and columns.contains('APa')">APa,</if>-->
<!--            <if test="columns != null and columns.contains('APb')">APb,</if>-->
<!--            <if test="columns != null and columns.contains('APc')">APc,</if>-->
<!--            <if test="columns != null and columns.contains('TAE')">TAE,</if>-->
<!--            <if test="columns != null and columns.contains('TPF')">TPF,</if>-->
<!--            <if test="columns != null and columns.contains('AE')">AE,</if>-->
<!--            <if test="columns != null and columns.contains('CE')">CE,</if>-->
<!--            <if test="columns != null and columns.contains('PAE')">PAE,</if>-->
<!--            <if test="columns != null and columns.contains('RAE')">RAE,</if>-->
<!--            <if test="columns != null and columns.contains('PRE')">PRE,</if>-->
<!--            <if test="columns != null and columns.contains('RRE')">RRE,</if>-->
<!--            <if test="columns != null and columns.contains('RPa')">RPa,</if>-->
<!--            <if test="columns != null and columns.contains('RPb')">RPb,</if>-->
<!--            <if test="columns != null and columns.contains('RPc')">RPc,</if>-->
<!--            <if test="columns != null and columns.contains('PFa')">PFa,</if>-->
<!--            <if test="columns != null and columns.contains('PFb')">PFb,</if>-->
<!--            <if test="columns != null and columns.contains('PFc')">PFc,</if>-->
<!--            <if test="columns != null and columns.contains('TRE')">TRE,</if>-->
<!--            <if test="columns != null and columns.contains('Fr')">FR</if>-->
<!--        </trim>-->
<!--        VALUES-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            #{point.pointId}, #{point.sampDateTime}, #{point.rcvDateTime}, #{point.insertDateTime},-->
<!--            &lt;!&ndash; 使用0作为默认值 &ndash;&gt;-->
<!--            <if test="columns != null and columns.contains('IA')">#{point.ia, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('IB')">#{point.ib, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('IC')">#{point.ic, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('UA')">#{point.ua, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('UB')">#{point.ub, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('UC')">#{point.uc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('ACTIVE_POWER')">#{point.activePower, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('ACTIVE_ENERGY')">#{point.activeEnergy, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TA')">#{point.ta, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TB')">#{point.tb, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TC')">#{point.tc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RUN_STATE')">#{point.runState, jdbcType=INTEGER},</if>-->
<!--            <if test="columns != null and columns.contains('FLAG')">#{point.flag, jdbcType=VARCHAR},</if>-->
<!--            <if test="columns != null and columns.contains('AP')">#{point.ap, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('APa')">#{point.apa, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('APb')">#{point.apb, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('APc')">#{point.apc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TAE')">#{point.tae, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TPF')">#{point.tpf, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('AE')">#{point.ae, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('CE')">#{point.ce, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PAE')">#{point.pae, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RAE')">#{point.rae, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PRE')">#{point.pre, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RRE')">#{point.rre, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RPa')">#{point.rpa, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RPb')">#{point.rpb, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('RPc')">#{point.rpc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PFa')">#{point.pfa, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PFb')">#{point.pfb, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('PFc')">#{point.pfc, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('TRE')">#{point.tre, jdbcType=DECIMAL, javaType=java.math.BigDecimal},</if>-->
<!--            <if test="columns != null and columns.contains('Fr')">#{point.fr, jdbcType=DECIMAL, javaType=java.math.BigDecimal}</if>-->
<!--        </trim>-->
<!--        ON DUPLICATE KEY UPDATE-->
<!--        RCV_DATETIME = VALUES(RCV_DATETIME),-->
<!--        INSERT_DATETIME = VALUES(INSERT_DATETIME)-->
<!--        &lt;!&ndash; 更新时只更新非0值 &ndash;&gt;-->
<!--        <if test="point.ia != null and point.ia.compareTo(new java.math.BigDecimal('0.0')) != 0">, IA = VALUES(IA)</if>-->
<!--        <if test="point.ib != null and point.ib.compareTo(new java.math.BigDecimal('0.0')) != 0">, IB = VALUES(IB)</if>-->
<!--        <if test="point.ic != null and point.ic.compareTo(new java.math.BigDecimal('0.0')) != 0">, IC = VALUES(IC)</if>-->
<!--        <if test="point.ua != null and point.ua.compareTo(new java.math.BigDecimal('0.0')) != 0">, UA = VALUES(UA)</if>-->
<!--        <if test="point.ub != null and point.ub.compareTo(new java.math.BigDecimal('0.0')) != 0">, UB = VALUES(UB)</if>-->
<!--        <if test="point.uc != null and point.uc.compareTo(new java.math.BigDecimal('0.0')) != 0">, UC = VALUES(UC)</if>-->
<!--        <if test="point.activePower != null and point.activePower.compareTo(new java.math.BigDecimal('0.0')) != 0">, ACTIVE_POWER = VALUES(ACTIVE_POWER)</if>-->
<!--        <if test="point.activeEnergy != null and point.activeEnergy.compareTo(new java.math.BigDecimal('0.0')) != 0">, ACTIVE_ENERGY = VALUES(ACTIVE_ENERGY)</if>-->
<!--        <if test="point.ta != null and point.ta.compareTo(new java.math.BigDecimal('0.0')) != 0">, TA = VALUES(TA)</if>-->
<!--        <if test="point.tb != null and point.tb.compareTo(new java.math.BigDecimal('0.0')) != 0">, TB = VALUES(TB)</if>-->
<!--        <if test="point.tc != null and point.tc.compareTo(new java.math.BigDecimal('0.0')) != 0">, TC = VALUES(TC)</if>-->
<!--        <if test="point.runState != null">, RUN_STATE = VALUES(RUN_STATE)</if>-->
<!--        <if test="point.flag != null">, FLAG = VALUES(FLAG)</if>-->
<!--        <if test="point.ap != null and point.ap.compareTo(new java.math.BigDecimal('0.0')) != 0">, AP = VALUES(AP)</if>-->
<!--        <if test="point.apa != null and point.apa.compareTo(new java.math.BigDecimal('0.0')) != 0">, APA = VALUES(APA)</if>-->
<!--        <if test="point.apb != null and point.apb.compareTo(new java.math.BigDecimal('0.0')) != 0">, APB = VALUES(APB)</if>-->
<!--        <if test="point.apc != null and point.apc.compareTo(new java.math.BigDecimal('0.0')) != 0">, APC = VALUES(APC)</if>-->
<!--        <if test="point.tae != null and point.tae.compareTo(new java.math.BigDecimal('0.0')) != 0">, TAE = VALUES(TAE)</if>-->
<!--        <if test="point.tpf != null and point.tpf.compareTo(new java.math.BigDecimal('0.0')) != 0">, TPF = VALUES(TPF)</if>-->
<!--        <if test="point.ae != null and point.ae.compareTo(new java.math.BigDecimal('0.0')) != 0">, AE = VALUES(AE)</if>-->
<!--        <if test="point.ce != null and point.ce.compareTo(new java.math.BigDecimal('0.0')) != 0">, CE = VALUES(CE)</if>-->
<!--        <if test="point.pae != null and point.pae.compareTo(new java.math.BigDecimal('0.0')) != 0">, PAE = VALUES(PAE)</if>-->
<!--        <if test="point.rae != null and point.rae.compareTo(new java.math.BigDecimal('0.0')) != 0">, RAE = VALUES(RAE)</if>-->
<!--        <if test="point.pre != null and point.pre.compareTo(new java.math.BigDecimal('0.0')) != 0">, PRE = VALUES(PRE)</if>-->
<!--        <if test="point.rre != null and point.rre.compareTo(new java.math.BigDecimal('0.0')) != 0">, RRE = VALUES(RRE)</if>-->
<!--        <if test="point.rpa != null and point.rpa.compareTo(new java.math.BigDecimal('0.0')) != 0">, RPA = VALUES(RPA)</if>-->
<!--        <if test="point.rpb != null and point.rpb.compareTo(new java.math.BigDecimal('0.0')) != 0">, RPB = VALUES(RPB)</if>-->
<!--        <if test="point.rpc != null and point.rpc.compareTo(new java.math.BigDecimal('0.0')) != 0">, RPC = VALUES(RPC)</if>-->
<!--        <if test="point.pfa != null and point.pfa.compareTo(new java.math.BigDecimal('0.0')) != 0">, PFA = VALUES(PFA)</if>-->
<!--        <if test="point.pfb != null and point.pfb.compareTo(new java.math.BigDecimal('0.0')) != 0">, PFB = VALUES(PFB)</if>-->
<!--        <if test="point.pfc != null and point.pfc.compareTo(new java.math.BigDecimal('0.0')) != 0">, PFC = VALUES(PFC)</if>-->
<!--        <if test="point.tre != null and point.tre.compareTo(new java.math.BigDecimal('0.0')) != 0">, TRE = VALUES(TRE)</if>-->
<!--        <if test="point.fr != null and point.fr.compareTo(new java.math.BigDecimal('0.0')) != 0">, FR = VALUES(FR)</if>-->
<!--    </insert>-->
<!--</mapper>-->
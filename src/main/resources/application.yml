server:
  port: 8080
spring:
  datasource:
#    url: ***********************************************************************************************************************
#    username: root
#    password: sc@20211123
#    url: *******************************************************************************************************************
#    username: root
#    password: PZUVvDKxVj
    url: jdbc:dm://100.112.4.48:5236/?&schema=sc_powermonitor
    username: scdb
    password: BYu4kWBhC7G-u7kV
#    url: *******************************************************************************************************************
#    username: root
#    password: sceeds@0721
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    driver-class-name: dm.jdbc.driver.DmDriver
    hikari:
      # 连接池最大连接数
      maximum-pool-size: 10
      # 最小空闲连接数
      minimum-idle: 5
      # 空闲连接超时时间，默认600000（10分钟）
      idle-timeout: 300000
      # 连接最大存活时间，默认1800000（30分钟）
      max-lifetime: 900000
      # 连接超时时间，默认30000（30秒）
      connection-timeout: 20000
      # 用于测试连接是否可用的查询语句
      connection-test-query: SELECT 1
      # 验证超时时间，默认5000（5秒）
      validation-timeout: 3000
      # 从连接池获取连接时是否检查连接有效性
      connection-init-sql: SELECT 1
      # 是否允许连接池暂停
      allow-pool-suspension: false
      # 连接泄漏检测阈值，默认0（不检测）
      leak-detection-threshold: 30000

  redis:
    host: 127.0.0.1
    port: 6379
    password: ''
    database: 3
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
      shutdown-timeout: 100ms

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.ecm.entity
  configuration:
    map-underscore-to-camel-case: true

netty:
  server:
    port: 8087
    boss-threads: 1
    worker-threads: 4

logging:
  config: classpath:logback-spring.xml
  level:
    com.example.ecm: INFO
    com.example.ecm.mapper: INFO
    org.springframework: INFO
    org.mybatis: INFO

message:
  log:
    path: logs